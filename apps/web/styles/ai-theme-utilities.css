/* AI主题工具类 - 基于shadcn/ui变量系统 */

/* 这些工具类可以直接在组件中使用，保持主题一致性 */

/* AI颜色工具类 */
.text-ai-primary { color: var(--ai-primary); }
.text-ai-secondary { color: var(--ai-secondary); }
.text-ai-accent { color: var(--ai-accent); }
.text-ai-success { color: var(--ai-success); }
.text-ai-warning { color: var(--ai-warning); }
.text-ai-processing { color: var(--ai-processing); }

.bg-ai-primary { background-color: var(--ai-primary); }
.bg-ai-secondary { background-color: var(--ai-secondary); }
.bg-ai-accent { background-color: var(--ai-accent); }
.bg-ai-success { background-color: var(--ai-success); }
.bg-ai-warning { background-color: var(--ai-warning); }
.bg-ai-processing { background-color: var(--ai-processing); }

.border-ai-primary { border-color: var(--ai-primary); }
.border-ai-secondary { border-color: var(--ai-secondary); }
.border-ai-accent { border-color: var(--ai-accent); }

/* AI渐变背景工具类 */
.bg-ai-gradient {
  background: linear-gradient(135deg, 
    var(--ai-primary) 0%, 
    var(--ai-secondary) 50%, 
    var(--ai-accent) 100%);
}

.bg-ai-gradient-subtle {
  background: linear-gradient(135deg, 
    var(--ai-primary) / 0.1 0%, 
    var(--ai-secondary) / 0.05 50%, 
    var(--ai-accent) / 0.1 100%);
}

.bg-ai-gradient-card {
  background: linear-gradient(135deg, 
    hsl(var(--card) / 0.95) 0%, 
    hsl(var(--card) / 0.9) 50%,
    hsl(var(--card) / 0.95) 100%);
}

/* AI边框工具类 */
.border-ai-glow {
  border: 1px solid var(--ai-primary) / 0.3;
  box-shadow: 0 0 8px var(--ai-glow);
}

.border-ai-subtle {
  border: 1px solid var(--ai-primary) / 0.15;
}

/* AI阴影工具类 */
.shadow-ai-sm {
  box-shadow: 0 2px 8px var(--ai-glow);
}

.shadow-ai-md {
  box-shadow: 
    0 4px 16px var(--ai-glow),
    0 2px 8px var(--ai-primary) / 0.1;
}

.shadow-ai-lg {
  box-shadow: 
    0 8px 32px var(--ai-glow),
    0 4px 16px var(--ai-primary) / 0.2;
}

.shadow-ai-xl {
  box-shadow: 
    0 12px 48px var(--ai-glow),
    0 8px 24px var(--ai-primary) / 0.3;
}

/* AI动画工具类 */
.animate-ai-pulse {
  animation: ai-pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-ai-glow {
  animation: ai-glow-cycle 3s ease-in-out infinite;
}

.animate-ai-float {
  animation: ai-float 4s ease-in-out infinite;
}

.animate-ai-shimmer {
  animation: ai-shimmer 2s linear infinite;
}

@keyframes ai-glow-cycle {
  0%, 100% {
    box-shadow: 0 0 8px var(--ai-glow);
  }
  50% {
    box-shadow: 0 0 20px var(--ai-glow);
  }
}

@keyframes ai-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes ai-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* AI状态工具类 */
.ai-status-processing {
  position: relative;
}

.ai-status-processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--ai-processing) / 0.1 50%, 
    transparent 100%);
  background-size: 200% 100%;
  animation: ai-shimmer 2s linear infinite;
  border-radius: inherit;
  pointer-events: none;
}

.ai-status-success {
  border-color: var(--ai-success) / 0.3;
  background: var(--ai-success) / 0.05;
}

.ai-status-error {
  border-color: hsl(var(--destructive)) / 0.3;
  background: hsl(var(--destructive)) / 0.05;
}

/* AI交互效果工具类 */
.ai-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px var(--ai-glow),
    0 4px 12px var(--ai-primary) / 0.2;
}

.ai-hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-hover-glow:hover {
  box-shadow: 0 0 20px var(--ai-glow);
  border-color: var(--ai-primary) / 0.5;
}

.ai-hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-hover-scale:hover {
  transform: scale(1.02);
}

/* AI文本效果工具类 */
.text-ai-gradient {
  background: linear-gradient(135deg, 
    var(--ai-primary) 0%, 
    var(--ai-secondary) 50%, 
    var(--ai-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-ai-gradient-animated {
  background: linear-gradient(135deg, 
    var(--ai-primary) 0%, 
    var(--ai-secondary) 50%, 
    var(--ai-accent) 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
}

/* AI布局工具类 */
.ai-container {
  background: var(--ai-gradient-subtle);
  border-radius: var(--radius);
  border: 1px solid var(--ai-primary) / 0.1;
}

.ai-section {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.8) 0%, 
    hsl(var(--background) / 0.9) 100%);
  backdrop-filter: blur(8px);
}

/* 响应式AI效果 */
@media (max-width: 768px) {
  .shadow-ai-lg {
    box-shadow: 
      0 4px 16px var(--ai-glow),
      0 2px 8px var(--ai-primary) / 0.1;
  }
  
  .ai-hover-lift:hover {
    transform: translateY(-1px);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .animate-ai-pulse,
  .animate-ai-glow,
  .animate-ai-float,
  .animate-ai-shimmer,
  .text-ai-gradient-animated {
    animation: none;
  }
  
  .ai-hover-lift,
  .ai-hover-scale {
    transition: none;
  }
}
