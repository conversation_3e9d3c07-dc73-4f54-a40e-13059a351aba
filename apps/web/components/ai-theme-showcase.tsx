'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Play,
  Pause
} from 'lucide-react';

import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Input } from '@kit/ui/input';
import { cn } from '@kit/ui/utils';

/**
 * AI主题展示组件
 * 演示如何使用新的AI科技感样式系统
 */
export function AIThemeShowcase() {
  const [progress, setProgress] = useState(65);
  const [isProcessing, setIsProcessing] = useState(false);

  const toggleProcessing = () => {
    setIsProcessing(!isProcessing);
  };

  return (
    <div className="space-y-8 p-6">
      {/* 标题区域 */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-ai-gradient-animated">
          AI科技感主题展示
        </h1>
        <p className="text-muted-foreground text-lg">
          基于shadcn/ui的AI驱动界面设计系统
        </p>
      </div>

      {/* 英雄区域 */}
      <div className="hero-elegant rounded-lg p-8 text-center space-y-6">
        <div className="inline-flex items-center gap-2 bg-ai-gradient-subtle px-4 py-2 rounded-full border-ai-subtle">
          <Sparkles className="h-4 w-4 text-ai-primary" />
          <span className="text-ai-primary font-semibold">AI Powered</span>
        </div>
        
        <h2 className="text-3xl font-bold text-gradient-elegant">
          Novel2Video AI Platform
        </h2>
        
        <p className="text-muted-foreground max-w-2xl mx-auto">
          体验下一代AI驱动的小说转视频平台，采用最新的科技感设计语言
        </p>

        <div className="flex gap-4 justify-center">
          <Button className="btn-elegant">
            <Play className="mr-2 h-4 w-4" />
            开始创作
          </Button>
          <Button variant="outline" className="ai-hover-glow">
            了解更多
          </Button>
        </div>
      </div>

      {/* 功能卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* AI处理状态卡片 */}
        <Card className="card-elegant ai-hover-lift">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Cpu className="h-5 w-5 text-ai-primary" />
                AI处理状态
              </CardTitle>
              <Badge className={cn("ai-badge", isProcessing ? "processing" : "success")}>
                {isProcessing ? "处理中" : "已完成"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>进度</span>
                <span className="text-ai-primary font-semibold">{progress}%</span>
              </div>
              <Progress 
                value={progress} 
                className={cn(
                  "progress-bar-animated",
                  isProcessing && "ai-status-processing"
                )}
              />
            </div>
            
            <Button 
              onClick={toggleProcessing}
              variant="outline" 
              size="sm"
              className="w-full ai-hover-scale"
            >
              {isProcessing ? (
                <>
                  <Pause className="mr-2 h-4 w-4" />
                  暂停处理
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  开始处理
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 状态指示器卡片 */}
        <Card className="ai-data-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="h-5 w-5 text-ai-secondary" />
              系统状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">AI模型</span>
                <div className="flex items-center gap-2">
                  <div className="status-indicator completed"></div>
                  <span className="text-sm text-ai-success">在线</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">视频渲染</span>
                <div className="flex items-center gap-2">
                  <div className="status-indicator processing"></div>
                  <span className="text-sm text-ai-processing">处理中</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">存储空间</span>
                <div className="flex items-center gap-2">
                  <div className="status-indicator draft"></div>
                  <span className="text-sm text-muted-foreground">75% 可用</span>
                </div>
              </div>
            </div>
            
            <hr className="ai-divider" />
            
            <div className="text-center">
              <span className="ai-highlight">系统运行正常</span>
            </div>
          </CardContent>
        </Card>

        {/* AI输入卡片 */}
        <Card className="bg-ai-gradient-card border-ai-glow">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-ai-accent animate-ai-pulse" />
              AI创作助手
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">输入您的创意</label>
              <Input 
                placeholder="描述您想要创作的故事..."
                className="ai-input"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Badge variant="outline" className="justify-center py-1">
                <CheckCircle className="mr-1 h-3 w-3" />
                智能
              </Badge>
              <Badge variant="outline" className="justify-center py-1">
                <Clock className="mr-1 h-3 w-3" />
                快速
              </Badge>
            </div>
            
            <Button className="w-full bg-ai-gradient text-white ai-hover-lift">
              <Sparkles className="mr-2 h-4 w-4" />
              开始AI创作
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 动画效果展示 */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-4 text-ai-primary">动画效果展示</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center space-y-2">
            <div className="w-16 h-16 mx-auto bg-ai-primary rounded-lg animate-ai-pulse"></div>
            <span className="text-sm">AI脉冲</span>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 mx-auto bg-ai-secondary rounded-lg animate-ai-glow"></div>
            <span className="text-sm">光晕效果</span>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 mx-auto bg-ai-accent rounded-lg animate-ai-float"></div>
            <span className="text-sm">浮动效果</span>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 mx-auto bg-ai-gradient rounded-lg animate-ai-shimmer"></div>
            <span className="text-sm">闪烁效果</span>
          </div>
        </div>
      </Card>

      {/* 颜色系统展示 */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-4 text-ai-primary">AI主题色彩系统</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            { name: 'AI Primary', class: 'bg-ai-primary', text: 'text-white' },
            { name: 'AI Secondary', class: 'bg-ai-secondary', text: 'text-white' },
            { name: 'AI Accent', class: 'bg-ai-accent', text: 'text-white' },
            { name: 'AI Success', class: 'bg-ai-success', text: 'text-white' },
            { name: 'AI Warning', class: 'bg-ai-warning', text: 'text-white' },
            { name: 'AI Processing', class: 'bg-ai-processing', text: 'text-white' },
          ].map((color) => (
            <div key={color.name} className="text-center space-y-2">
              <div className={cn("w-full h-16 rounded-lg", color.class)}></div>
              <span className="text-xs font-medium">{color.name}</span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
