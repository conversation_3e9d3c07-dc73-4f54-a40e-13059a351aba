# AI科技感主题设计系统

## 概述

这是一套基于shadcn/ui的AI科技感主题设计系统，专为Novel2AI网站设计。它保持了shadcn/ui的所有优点，同时添加了现代科技感的视觉效果。

## 设计理念

### 🎨 颜色哲学
- **主色调**: 深蓝紫色 (oklch(45% 0.15 260)) - 体现AI的神秘感和专业性
- **次要色**: 青蓝色 (oklch(70% 0.10 200)) - 代表创新和创造力  
- **强调色**: 青绿色 (oklch(60% 0.08 180)) - 象征技术的活力
- **成功色**: 绿色调 (oklch(60% 0.12 150)) - 表示完成和成功
- **处理色**: 蓝紫色 (oklch(65% 0.12 260)) - 表示AI正在工作

### 🌓 深浅模式适配
所有颜色都使用OKLCH色彩空间，确保在深色和浅色模式下都有完美的对比度和视觉效果。

## 核心特性

### 1. CSS变量系统
```css
/* AI主题变量 */
--ai-primary: oklch(45% 0.15 260);
--ai-secondary: oklch(70% 0.10 200);
--ai-accent: oklch(60% 0.08 180);
--ai-glow: oklch(45% 0.15 260 / 0.3);
--ai-success: oklch(60% 0.12 150);
--ai-warning: oklch(70% 0.15 60);
--ai-processing: oklch(65% 0.12 260);
```

### 2. 工具类系统
```css
/* 颜色工具类 */
.text-ai-primary { color: var(--ai-primary); }
.bg-ai-gradient { background: linear-gradient(...); }
.border-ai-glow { border: 1px solid var(--ai-primary) / 0.3; }

/* 动画工具类 */
.animate-ai-pulse { animation: ai-pulse 2.5s infinite; }
.animate-ai-glow { animation: ai-glow-cycle 3s infinite; }
.ai-hover-lift:hover { transform: translateY(-2px); }
```

### 3. 组件样式类
```css
/* 高级组件样式 */
.btn-elegant { /* AI科技感按钮 */ }
.card-elegant { /* AI科技感卡片 */ }
.ai-data-card { /* AI数据展示卡片 */ }
.progress-elegant { /* AI进度条 */ }
```

## 使用指南

### 基础用法

#### 1. 文本颜色
```tsx
<h1 className="text-ai-primary">AI主标题</h1>
<p className="text-ai-secondary">次要文本</p>
<span className="text-ai-gradient-animated">动画渐变文字</span>
```

#### 2. 背景和边框
```tsx
<div className="bg-ai-gradient-subtle border-ai-glow">
  AI主题容器
</div>
```

#### 3. 按钮样式
```tsx
<Button className="btn-elegant ai-hover-lift">
  AI科技感按钮
</Button>
```

#### 4. 卡片组件
```tsx
<Card className="ai-data-card">
  <CardContent>AI数据卡片</CardContent>
</Card>
```

### 状态指示

#### 1. 处理状态
```tsx
<div className="ai-status-processing">
  <Badge className="ai-badge processing">处理中</Badge>
</div>
```

#### 2. 成功状态
```tsx
<div className="ai-status-success">
  <Badge className="ai-badge success">已完成</Badge>
</div>
```

#### 3. 状态指示器
```tsx
<div className="status-indicator processing"></div>
<div className="status-indicator completed"></div>
<div className="status-indicator failed"></div>
```

### 动画效果

#### 1. 基础动画
```tsx
<div className="animate-ai-pulse">脉冲动画</div>
<div className="animate-ai-glow">光晕动画</div>
<div className="animate-ai-float">浮动动画</div>
```

#### 2. 交互动画
```tsx
<Button className="ai-hover-lift">悬停上升</Button>
<Card className="ai-hover-glow">悬停光晕</Card>
<div className="ai-hover-scale">悬停缩放</div>
```

### 进度条和加载

#### 1. AI主题进度条
```tsx
<Progress className="progress-elegant" value={75} />
```

#### 2. 加载状态
```tsx
<div className="ai-loading">
  <div className="ai-status-processing">
    加载中的内容
  </div>
</div>
```

## 最佳实践

### 1. 保持一致性
- 始终使用AI主题变量而不是硬编码颜色
- 遵循shadcn/ui的组件结构和命名约定
- 保持深浅模式的兼容性

### 2. 性能优化
- 使用CSS变量确保主题切换的流畅性
- 合理使用动画，避免过度使用影响性能
- 支持用户的减少动画偏好设置

### 3. 可访问性
- 所有颜色组合都经过对比度测试
- 动画支持prefers-reduced-motion
- 保持良好的键盘导航体验

### 4. 响应式设计
- 在移动设备上适当减少动画效果
- 调整阴影和光晕效果的强度
- 保持触摸友好的交互区域

## 文件结构

```
apps/web/styles/
├── shadcn-ui.css          # 主题颜色定义
├── novel2video.css        # AI主题核心样式
├── ai-theme-utilities.css # AI主题工具类
└── globals.css           # 全局样式导入
```

## 扩展指南

### 添加新的AI主题颜色
1. 在`shadcn-ui.css`中定义CSS变量
2. 在`ai-theme-utilities.css`中添加对应的工具类
3. 更新TypeScript类型定义（如需要）

### 创建新的组件样式
1. 在`novel2video.css`中定义组件样式类
2. 使用现有的AI主题变量
3. 确保深浅模式兼容性
4. 添加适当的动画和交互效果

## 示例页面

访问 `/home/<USER>
