'use client';

import { useEffect, useState } from 'react';

import {
  CalendarIcon,
  FileTextIcon,
  MoreHorizontalIcon,
  PlayIcon,
  CheckCircleIcon,
  ClockIcon,
  AlertTriangle,
} from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Progress } from '@kit/ui/progress';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { getProjectsAction } from '../_lib/server/server-actions';

type ProjectStatus = 'draft' | 'processing' | 'completed' | 'failed';

interface ProjectData {
  id: string;
  title: string;
  description?: string | null;
  status: ProjectStatus;
  progress: number;
  segmentCount?: number;
  created_at: string;
}

const statusConfig = {
  draft: {
    label: 'novel2video:status.project.draft',
    variant: 'secondary' as const,
    icon: ClockIcon,
    color: 'text-muted-foreground',
    badgeClass: 'ai-badge',
    indicatorClass: 'status-indicator draft',
  },
  processing: {
    label: 'novel2video:status.project.processing',
    variant: 'default' as const,
    icon: ClockIcon,
    color: 'text-ai-processing',
    badgeClass: 'ai-badge processing',
    indicatorClass: 'status-indicator processing',
  },
  completed: {
    label: 'novel2video:status.project.completed',
    variant: 'default' as const,
    icon: CheckCircleIcon,
    color: 'text-ai-success',
    badgeClass: 'ai-badge success',
    indicatorClass: 'status-indicator completed',
  },
  failed: {
    label: 'novel2video:status.project.failed',
    variant: 'destructive' as const,
    icon: AlertTriangle,
    color: 'text-destructive',
    badgeClass: 'ai-badge',
    indicatorClass: 'status-indicator failed',
  },
};

export function ProjectsList() {
  const [projects, setProjects] = useState<ProjectData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const result = await getProjectsAction({});
        if (result.success) {
          setProjects(result.data || []);
        }
      } catch (error) {
        console.error('Failed to load projects:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, []);

  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-4 w-10" />
              </div>
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-9 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="space-y-6">
          <div className="mx-auto h-16 w-16 rounded-full bg-ai-gradient-subtle flex items-center justify-center border-ai-subtle animate-ai-pulse">
            <FileTextIcon className="h-8 w-8 text-ai-primary" />
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-semibold text-ai-gradient">
              <Trans i18nKey="novel2video:dashboard.empty.title" />
            </h3>
            <p className="text-muted-foreground max-w-md text-base">
              <Trans i18nKey="novel2video:dashboard.empty.description" />
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}

interface ProjectCardProps {
  project: ProjectData;
}

function ProjectCard({ project }: ProjectCardProps) {
  const StatusIcon = statusConfig[project.status].icon;
  const config = statusConfig[project.status];

  return (
    <Card className={cn(
      "ai-data-card group relative overflow-hidden transition-all duration-300",
      project.status === 'processing' && "ai-status-processing",
      project.status === 'completed' && "ai-status-success",
      project.status === 'failed' && "ai-status-error"
    )}>
      <CardHeader className="space-y-3 pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <h3 className="line-clamp-2 text-lg font-semibold leading-tight">
              {project.title}
            </h3>
            <If condition={Boolean(project.description)}>
              <p className="line-clamp-2 text-sm text-muted-foreground">
                {project.description}
              </p>
            </If>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity ai-hover-scale"
              >
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem className="text-destructive focus:text-destructive">
                <AlertTriangle className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:delete" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status and progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={config.indicatorClass}></div>
              <StatusIcon className={`h-4 w-4 ${config.color}`} />
              <Badge className={cn("text-xs", config.badgeClass)}>
                <Trans i18nKey={config.label} />
              </Badge>
            </div>
            <span className={cn(
              "text-xs font-semibold",
              project.status === 'processing' ? "text-ai-processing animate-ai-pulse" : "text-muted-foreground"
            )}>
              {project.progress}%
            </span>
          </div>

          <If condition={project.progress > 0}>
            <Progress
              value={project.progress}
              className={cn(
                "h-2 progress-bar-animated",
                project.status === 'processing' && "progress-elegant"
              )}
            />
          </If>
        </div>

        {/* Project metadata */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <FileTextIcon className="h-3 w-3" />
            <span>
              <Trans
                i18nKey="novel2video:projects.segmentCount"
                values={{ count: project.segmentCount || 0 }}
              />
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <CalendarIcon className="h-3 w-3" />
            <span>{new Date(project.created_at).toLocaleDateString()}</span>
          </div>
        </div>

        {/* Action button */}
        <Button asChild className={cn(
          "w-full ai-hover-lift",
          project.status === 'processing' && "btn-elegant",
          project.status === 'completed' && "bg-ai-success hover:bg-ai-success/90"
        )} size="sm">
          <Link href={`/home/<USER>/projects/${project.id}`}>
            <PlayIcon className="mr-2 h-4 w-4" />
            <Trans i18nKey="novel2video:actions.open" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}