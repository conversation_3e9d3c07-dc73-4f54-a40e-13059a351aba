'use client';

import { useState } from 'react';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { handleAsyncOperation, createActionHandler } from '../_lib/utils/error-handling';
import { 
  startProcessingAction, 
  pauseProcessingAction,
  generateSingleSegmentImageAction 
} from '../_lib/server/actions/project-actions';

/**
 * 错误处理使用示例组件
 * 展示如何正确处理server action的错误信息
 */
export function ErrorHandlingExample({ projectId }: { projectId: string }) {
  const [isProcessing, setIsProcessing] = useState(false);

  // 方法1: 使用 handleAsyncOperation 包装异步操作
  const handleStartProcessing = async () => {
    setIsProcessing(true);
    
    const success = await handleAsyncOperation(
      () => startProcessingAction({ projectId }),
      {
        successMessage: '项目处理已开始',
        errorMessage: '开始处理项目失败',
        onSuccess: (data) => {
          console.log('处理开始成功:', data);
          // 更新UI状态
        },
        onError: (error) => {
          console.error('处理开始失败:', error);
          setIsProcessing(false);
        }
      }
    );

    if (!success) {
      setIsProcessing(false);
    }
  };

  // 方法2: 使用 createActionHandler 创建可重用的处理器
  const pauseProcessingHandler = createActionHandler(
    pauseProcessingAction,
    '暂停项目处理失败'
  );

  const handlePauseProcessing = async () => {
    await pauseProcessingHandler(
      { projectId },
      {
        successMessage: '项目处理已暂停',
        onSuccess: () => {
          setIsProcessing(false);
          // 更新UI状态
        }
      }
    );
  };

  // 方法3: 传统的try-catch方式（改进版）
  const handleGenerateImage = async (segmentId: string) => {
    try {
      const result = await generateSingleSegmentImageAction({ segmentId });
      
      if (result.success) {
        // 使用action返回的具体成功消息
        console.log('图片生成成功:', result.message || '图片生成任务已创建');
      } else {
        // 显示action返回的具体错误信息
        const errorMessage = 'error' in result ? String(result.error) : '生成图片失败';
        console.error('图片生成失败:', errorMessage);
      }
    } catch (error) {
      // 处理网络错误或其他异常
      const errorMessage = error instanceof Error ? error.message : '生成图片时发生未知错误';
      console.error('图片生成异常:', errorMessage);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>错误处理示例</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium">方法1: handleAsyncOperation</h4>
          <Button 
            onClick={handleStartProcessing}
            disabled={isProcessing}
            className="w-full"
          >
            {isProcessing ? '处理中...' : '开始处理'}
          </Button>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">方法2: createActionHandler</h4>
          <Button 
            onClick={handlePauseProcessing}
            variant="outline"
            className="w-full"
          >
            暂停处理
          </Button>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">方法3: 传统try-catch（改进版）</h4>
          <Button 
            onClick={() => handleGenerateImage('segment-id')}
            variant="secondary"
            className="w-full"
          >
            生成图片
          </Button>
        </div>

        <div className="mt-4 p-3 bg-muted rounded-lg">
          <h5 className="font-medium text-sm mb-2">关键改进点:</h5>
          <ul className="text-xs space-y-1 text-muted-foreground">
            <li>• 显示action返回的具体错误信息，而不是通用错误</li>
            <li>• 区分处理action错误和网络异常</li>
            <li>• 提供用户友好的错误消息</li>
            <li>• 统一的错误处理模式</li>
            <li>• 支持成功和错误回调</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 错误处理最佳实践说明
 */
export const ErrorHandlingBestPractices = {
  // ✅ 正确的做法
  good: {
    // 显示具体的错误信息
    showSpecificError: `
      if (result.success) {
        toast.success(result.message || '操作成功');
      } else {
        // 显示action返回的具体错误信息
        const errorMessage = 'error' in result ? String(result.error) : '操作失败';
        toast.error(errorMessage);
      }
    `,
    
    // 区分不同类型的错误
    handleDifferentErrors: `
      try {
        const result = await someAction(data);
        // 处理action返回的业务错误
        if (!result.success) {
          toast.error(result.error || '业务操作失败');
          return;
        }
        // 处理成功情况
        toast.success(result.message || '操作成功');
      } catch (error) {
        // 处理网络错误或系统异常
        const errorMessage = error instanceof Error ? error.message : '系统错误';
        toast.error(errorMessage);
      }
    `,
  },

  // ❌ 错误的做法
  bad: {
    // 显示通用错误信息
    showGenericError: `
      try {
        await someAction(data);
        toast.success('操作成功');
      } catch (error) {
        // ❌ 用户不知道具体出了什么问题
        toast.error('操作失败');
      }
    `,
    
    // 忽略action返回的错误信息
    ignoreActionError: `
      const result = await someAction(data);
      if (result.success) {
        toast.success('操作成功');
      } else {
        // ❌ 没有使用action返回的具体错误信息
        toast.error('操作失败');
      }
    `,
  }
};
