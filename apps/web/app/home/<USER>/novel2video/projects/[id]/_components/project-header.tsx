'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlayIcon,
  PauseIcon,
  RefreshCwIcon,
  SettingsIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  VideoIcon,
  CalendarIcon,
  HashIcon,
  TrendingUpIcon
} from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import { Progress } from '@kit/ui/progress';
import { Trans } from '@kit/ui/trans';

interface ProjectHeaderProps {
  project: any;
  segmentsCount: number;
  isProcessing: boolean;
  onRefresh: () => void;
  onResegmentText: () => void;
  onStartProcessing: () => void;
  onPauseProcessing: () => void;
  getStatusVariant: (status: string) => 'default' | 'secondary' | 'destructive';
  getStatusText: (status: string) => React.ReactNode;
}

export function ProjectHeader({
  project,
  segmentsCount,
  isProcessing,
  onRefresh,
  onResegmentText,
  onStartProcessing,
  onPauseProcessing,
  getStatusVariant,
  getStatusText
}: ProjectHeaderProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const createdDate = new Date(project.created_at).toLocaleDateString('zh-CN');
  const overallProgress = project.progress || 0;

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Compact main info row */}
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-4 min-w-0 flex-1">
          {/* Project icon and title */}
          <div className="flex items-center gap-3 min-w-0">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
              <VideoIcon className="h-5 w-5 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg font-semibold truncate">{project.title}</h1>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <CalendarIcon className="h-3 w-3" />
                <span>{createdDate}</span>
                <Separator orientation="vertical" className="h-3" />
                <HashIcon className="h-3 w-3" />
                <span>
                  <Trans
                    i18nKey="novel2video:project.segmentsUnit"
                    values={{ count: segmentsCount }}
                    defaults="{{count}} segments"
                  />
                </span>
              </div>
            </div>
          </div>

          {/* Status and progress */}
          <div className="flex items-center gap-3">
            <Badge variant={getStatusVariant(project.status)} className="font-medium">
              {getStatusText(project.status)}
            </Badge>
            <div className="flex items-center gap-2">
              <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
              <div className="flex items-center gap-2">
                <Progress value={overallProgress} className="w-20 h-2" />
                <span className="text-sm font-medium text-muted-foreground min-w-[3ch]">
                  {Math.round(overallProgress)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Action button group */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-1"
          >
            <Trans i18nKey="novel2video:project.details.buttonText" />
            {isExpanded ? (
              <ChevronUpIcon className="h-3 w-3" />
            ) : (
              <ChevronDownIcon className="h-3 w-3" />
            )}
          </Button>

          <Separator orientation="vertical" className="h-4" />

          <Button variant="ghost" size="sm" onClick={onRefresh}>
            <RefreshCwIcon className="h-4 w-4" />
          </Button>

          {isProcessing ? (
            <Button variant="outline" size="sm" onClick={onPauseProcessing}>
              <PauseIcon className="h-4 w-4" />
              <span className="ml-1 text-xs">
                <Trans i18nKey="novel2video:project.pause" />
              </span>
            </Button>
          ) : (
            <Button size="sm" onClick={onStartProcessing}>
              <PlayIcon className="h-4 w-4" />
              <span className="ml-1 text-xs">
                <Trans i18nKey="novel2video:project.startProcessing" />
              </span>
            </Button>
          )}
        </div>
      </div>

      {/* Expandable detailed information */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-3 pt-0">
              <Separator className="mb-3" />
              {project.description && (
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {project.description}
                </p>
              )}

              {/* Detailed action buttons */}
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={onResegmentText}>
                  <Trans i18nKey="novel2video:project.resegmentText" defaults="重新分段" />
                </Button>
                <Button variant="outline" size="sm">
                  <SettingsIcon className="h-4 w-4 mr-1" />
                  <Trans i18nKey="novel2video:project.settings" defaults="项目设置" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
