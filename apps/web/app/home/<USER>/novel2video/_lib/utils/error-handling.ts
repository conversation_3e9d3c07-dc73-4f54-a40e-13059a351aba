/**
 * 错误处理工具函数
 * 用于统一处理server action的错误信息显示
 */

import { toast } from '@kit/ui/sonner';

/**
 * Server Action的标准返回类型
 */
export interface ActionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * 处理server action的结果，自动显示成功或错误消息
 * @param result - server action的返回结果
 * @param options - 配置选项
 */
export function handleActionResult<T>(
  result: ActionResult<T>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    showSuccessToast?: boolean;
    showErrorToast?: boolean;
  } = {}
): boolean {
  const {
    successMessage,
    errorMessage,
    showSuccessToast = true,
    showErrorToast = true,
  } = options;

  if (result.success) {
    if (showSuccessToast) {
      const message = successMessage || result.message || '操作成功';
      toast.success(message);
    }
    return true;
  } else {
    if (showErrorToast) {
      const message = result.error || errorMessage || '操作失败';
      toast.error(message);
    }
    return false;
  }
}

/**
 * 处理异步操作的错误，包装try-catch逻辑
 * @param operation - 要执行的异步操作
 * @param options - 配置选项
 */
export async function handleAsyncOperation<T>(
  operation: () => Promise<ActionResult<T>>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    showSuccessToast?: boolean;
    showErrorToast?: boolean;
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
  } = {}
): Promise<boolean> {
  const {
    successMessage,
    errorMessage,
    showSuccessToast = true,
    showErrorToast = true,
    onSuccess,
    onError,
  } = options;

  try {
    const result = await operation();
    
    if (result.success) {
      if (showSuccessToast) {
        const message = successMessage || result.message || '操作成功';
        toast.success(message);
      }
      if (onSuccess && result.data) {
        onSuccess(result.data);
      }
      return true;
    } else {
      const error = result.error || errorMessage || '操作失败';
      if (showErrorToast) {
        toast.error(error);
      }
      if (onError) {
        onError(error);
      }
      return false;
    }
  } catch (error) {
    console.error('异步操作失败:', error);
    const errorMsg = error instanceof Error ? error.message : (errorMessage || '操作时发生未知错误');
    
    if (showErrorToast) {
      toast.error(errorMsg);
    }
    if (onError) {
      onError(errorMsg);
    }
    return false;
  }
}

/**
 * 从错误对象中提取错误消息
 * @param error - 错误对象
 * @param fallbackMessage - 默认错误消息
 */
export function extractErrorMessage(error: unknown, fallbackMessage = '发生未知错误'): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message);
  }
  return fallbackMessage;
}

/**
 * 从action结果中提取错误消息
 * @param result - action结果
 * @param fallbackMessage - 默认错误消息
 */
export function extractActionError(result: any, fallbackMessage = '操作失败'): string {
  if ('error' in result && result.error) {
    return String(result.error);
  }
  if ('message' in result && result.message) {
    return String(result.message);
  }
  return fallbackMessage;
}

/**
 * 创建一个带有错误处理的action调用器
 * @param action - server action函数
 * @param defaultErrorMessage - 默认错误消息
 */
export function createActionHandler<TInput, TOutput>(
  action: (input: TInput) => Promise<ActionResult<TOutput>>,
  defaultErrorMessage = '操作失败'
) {
  return async (
    input: TInput,
    options: {
      successMessage?: string;
      errorMessage?: string;
      showSuccessToast?: boolean;
      showErrorToast?: boolean;
      onSuccess?: (data: TOutput) => void;
      onError?: (error: string) => void;
    } = {}
  ): Promise<boolean> => {
    return handleAsyncOperation(
      () => action(input),
      {
        errorMessage: defaultErrorMessage,
        ...options,
      }
    );
  };
}

/**
 * 验证action结果的类型守卫
 * @param result - 要验证的结果
 */
export function isActionResult(result: any): result is ActionResult {
  return (
    result &&
    typeof result === 'object' &&
    'success' in result &&
    typeof result.success === 'boolean'
  );
}

/**
 * 创建一个标准的错误响应
 * @param error - 错误信息
 */
export function createErrorResult(error: string): ActionResult<never> {
  return {
    success: false,
    error,
  };
}

/**
 * 创建一个标准的成功响应
 * @param data - 返回数据
 * @param message - 成功消息
 */
export function createSuccessResult<T>(data: T, message?: string): ActionResult<T> {
  return {
    success: true,
    data,
    message,
  };
}

/**
 * 用于React组件的错误处理hook
 */
export function useErrorHandler() {
  const handleError = (error: unknown, fallbackMessage = '操作失败') => {
    const errorMessage = extractErrorMessage(error, fallbackMessage);
    toast.error(errorMessage);
    console.error('操作错误:', error);
  };

  const handleActionResult = <T>(
    result: ActionResult<T>,
    options: {
      successMessage?: string;
      errorMessage?: string;
      onSuccess?: (data: T) => void;
    } = {}
  ): boolean => {
    if (result.success) {
      if (options.successMessage || result.message) {
        toast.success(options.successMessage || result.message!);
      }
      if (options.onSuccess && result.data) {
        options.onSuccess(result.data);
      }
      return true;
    } else {
      const errorMessage = result.error || options.errorMessage || '操作失败';
      toast.error(errorMessage);
      return false;
    }
  };

  return {
    handleError,
    handleActionResult,
  };
}
